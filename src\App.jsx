import React, { useState, useEffect } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Import components
import Navbar from './components/Navbar';
import Sidebar from './components/Sidebar';
import LoadingSpinner from './components/LoadingSpinner';

function App() {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [activeMenuItem, setActiveMenuItem] = useState('dashboard');

  const location = useLocation();

  // Mock user data - replace with actual auth logic
  useEffect(() => {
    // Simulate loading user data
    setTimeout(() => {
      // Mock user - replace with actual auth check
      const mockUser = {
        id: 1,
        name: '<PERSON>',
        email: '<EMAIL>',
        role: 'student', // admin, professor, student
        avatar: null
      };
      setUser(mockUser);
      setIsLoading(false);
    }, 1000);
  }, []);

  // Update active menu item based on current route
  useEffect(() => {
    const path = location.pathname;
    if (path.includes('/dashboard')) {
      setActiveMenuItem('dashboard');
    } else if (path.includes('/users')) {
      setActiveMenuItem('users');
    } else if (path.includes('/courses')) {
      setActiveMenuItem('courses');
    } else if (path.includes('/quizzes')) {
      setActiveMenuItem('quizzes');
    } else if (path.includes('/analytics')) {
      setActiveMenuItem('analytics');
    } else if (path.includes('/upload')) {
      setActiveMenuItem('upload');
    } else if (path.includes('/students')) {
      setActiveMenuItem('students');
    } else if (path.includes('/progress')) {
      setActiveMenuItem('progress');
    } else if (path.includes('/recommendations')) {
      setActiveMenuItem('recommendations');
    } else if (path.includes('/settings')) {
      setActiveMenuItem('settings');
    }
  }, [location]);

  const handleSidebarToggle = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const handleSidebarClose = () => {
    setIsSidebarOpen(false);
  };

  const handleMenuItemClick = (item) => {
    setActiveMenuItem(item.id);
    // Navigation will be handled by React Router
    console.log('Navigate to:', item.path);
  };

  const handleLogout = () => {
    setUser(null);
    // Clear auth tokens, redirect to login, etc.
    console.log('Logout');
  };

  // Mock notifications
  const notifications = [
    {
      id: 1,
      title: 'New Assignment',
      message: 'You have a new assignment in Mathematics',
      time: '2 hours ago',
      read: false
    },
    {
      id: 2,
      title: 'Grade Updated',
      message: 'Your grade for Physics Quiz has been updated',
      time: '1 day ago',
      read: true
    }
  ];

  // Show loading screen while initializing
  if (isLoading) {
    return (
      <LoadingSpinner
        fullScreen
        size="lg"
        text="Loading EduPlatform..."
      />
    );
  }

  // Check if current route should show layout (not login/register)
  const isAuthPage = location.pathname === '/login' || location.pathname === '/register';
  const shouldShowLayout = user && !isAuthPage;

  return (
    <div className="min-h-screen bg-base-200">
      {shouldShowLayout && (
        <>
          {/* Navbar */}
          <Navbar
            user={user}
            onToggleSidebar={handleSidebarToggle}
            onLogout={handleLogout}
            notifications={notifications}
          />

          {/* Layout with Sidebar */}
          <div className="flex">
            <Sidebar
              isOpen={isSidebarOpen}
              userRole={user?.role}
              activeItem={activeMenuItem}
              onItemClick={handleMenuItemClick}
              onClose={handleSidebarClose}
            />

            {/* Main Content */}
            <main className={`
              flex-1 transition-all duration-300 ease-in-out
              ${isSidebarOpen ? 'lg:ml-70' : ''}
              min-h-screen
            `}>
              <div className="p-6">
                <Outlet />
              </div>
            </main>
          </div>
        </>
      )}

      {/* Auth pages without layout */}
      {!shouldShowLayout && (
        <div className="min-h-screen">
          <Outlet />
        </div>
      )}

      {/* Toast notifications */}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
      />
    </div>
  );
}

export default App;
