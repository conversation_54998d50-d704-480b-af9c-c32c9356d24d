import React from 'react';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import App from './App';
import { useAuth } from './contexts/AuthContext';

// Import pages
import Login from './pages/Login';
import Register from './pages/Register';
import AdminDashboard from './pages/AdminDashboard';
import ProfessorDashboard from './pages/ProfessorDashboard';
import StudentDashboard from './pages/StudentDashboard';

// Protected Route Component
const ProtectedRoute = ({ children, allowedRoles = [] }) => {
  const { user, isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (allowedRoles.length > 0 && !allowedRoles.includes(user?.role)) {
    return <Navigate to="/unauthorized" replace />;
  }

  return children;
};

// Role-based redirect component
const RoleBasedRedirect = () => {
  const { user, isAuthenticated, isLoading, getDashboardRoute } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="loading loading-spinner loading-lg"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <Navigate to={getDashboardRoute()} replace />;
};

// Error boundary component
const ErrorPage = () => (
  <div className="min-h-screen flex items-center justify-center bg-base-100">
    <div className="text-center">
      <h1 className="text-6xl font-bold text-error mb-4">404</h1>
      <h2 className="text-2xl font-semibold text-base-content mb-4">Page Not Found</h2>
      <p className="text-base-content/60 mb-8">
        The page you're looking for doesn't exist.
      </p>
      <a href="/" className="btn btn-primary">
        Go Home
      </a>
    </div>
  </div>
);

const UnauthorizedPage = () => (
  <div className="min-h-screen flex items-center justify-center bg-base-100">
    <div className="text-center">
      <h1 className="text-6xl font-bold text-warning mb-4">403</h1>
      <h2 className="text-2xl font-semibold text-base-content mb-4">Access Denied</h2>
      <p className="text-base-content/60 mb-8">
        You don't have permission to access this page.
      </p>
      <a href="/" className="btn btn-primary">
        Go Home
      </a>
    </div>
  </div>
);

// Router configuration
export const router = createBrowserRouter([
  {
    path: "/",
    element: <App />,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        element: <RoleBasedRedirect />
      },
      {
        path: "login",
        element: <Login />
      },
      {
        path: "register",
        element: <Register />
      },
      {
        path: "unauthorized",
        element: <UnauthorizedPage />
      },
      // Admin routes
      {
        path: "admin",
        children: [
          {
            path: "dashboard",
            element: (
              <ProtectedRoute allowedRoles={['admin']}>
                <AdminDashboard />
              </ProtectedRoute>
            )
          },
          {
            path: "users",
            element: (
              <ProtectedRoute allowedRoles={['admin']}>
                <div className="p-8"><h1>User Management</h1></div>
              </ProtectedRoute>
            )
          },
          {
            path: "courses",
            element: (
              <ProtectedRoute allowedRoles={['admin']}>
                <div className="p-8"><h1>Course Management</h1></div>
              </ProtectedRoute>
            )
          },
          {
            path: "analytics",
            element: (
              <ProtectedRoute allowedRoles={['admin']}>
                <div className="p-8"><h1>System Analytics</h1></div>
              </ProtectedRoute>
            )
          },
          {
            path: "settings",
            element: (
              <ProtectedRoute allowedRoles={['admin']}>
                <div className="p-8"><h1>System Settings</h1></div>
              </ProtectedRoute>
            )
          }
        ]
      },
      // Professor routes
      {
        path: "professor",
        children: [
          {
            path: "dashboard",
            element: (
              <ProtectedRoute allowedRoles={['professor']}>
                <ProfessorDashboard />
              </ProtectedRoute>
            )
          },
          {
            path: "courses",
            element: (
              <ProtectedRoute allowedRoles={['professor']}>
                <div className="p-8"><h1>My Courses</h1></div>
              </ProtectedRoute>
            )
          },
          {
            path: "upload",
            element: (
              <ProtectedRoute allowedRoles={['professor']}>
                <div className="p-8"><h1>Upload Lecture</h1></div>
              </ProtectedRoute>
            )
          },
          {
            path: "quizzes",
            element: (
              <ProtectedRoute allowedRoles={['professor']}>
                <div className="p-8"><h1>Manage Quizzes</h1></div>
              </ProtectedRoute>
            )
          },
          {
            path: "students",
            element: (
              <ProtectedRoute allowedRoles={['professor']}>
                <div className="p-8"><h1>Student Performance</h1></div>
              </ProtectedRoute>
            )
          },
          {
            path: "analytics",
            element: (
              <ProtectedRoute allowedRoles={['professor']}>
                <div className="p-8"><h1>Course Analytics</h1></div>
              </ProtectedRoute>
            )
          }
        ]
      },
      // Student routes
      {
        path: "student",
        children: [
          {
            path: "dashboard",
            element: (
              <ProtectedRoute allowedRoles={['student']}>
                <StudentDashboard />
              </ProtectedRoute>
            )
          },
          {
            path: "courses",
            element: (
              <ProtectedRoute allowedRoles={['student']}>
                <div className="p-8"><h1>My Courses</h1></div>
              </ProtectedRoute>
            )
          },
          {
            path: "quizzes",
            element: (
              <ProtectedRoute allowedRoles={['student']}>
                <div className="p-8"><h1>Take Quizzes</h1></div>
              </ProtectedRoute>
            )
          },
          {
            path: "progress",
            element: (
              <ProtectedRoute allowedRoles={['student']}>
                <div className="p-8"><h1>My Progress</h1></div>
              </ProtectedRoute>
            )
          },
          {
            path: "recommendations",
            element: (
              <ProtectedRoute allowedRoles={['student']}>
                <div className="p-8"><h1>Recommendations</h1></div>
              </ProtectedRoute>
            )
          }
        ]
      }
    ]
  }
]);

export default router;
