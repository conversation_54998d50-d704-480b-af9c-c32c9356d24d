import React from 'react';
import { motion } from 'framer-motion';

const LoadingSpinner = ({ 
  size = 'md', 
  color = 'primary', 
  text = '', 
  fullScreen = false,
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  const colorClasses = {
    primary: 'border-primary',
    secondary: 'border-secondary',
    success: 'border-success',
    warning: 'border-warning',
    error: 'border-error',
    info: 'border-info'
  };

  const spinnerElement = (
    <motion.div
      className={`
        inline-block border-4 border-t-transparent rounded-full animate-spin
        ${sizeClasses[size]} ${colorClasses[color]} ${className}
      `}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    />
  );

  const content = (
    <div className="flex flex-col items-center justify-center gap-3">
      {spinnerElement}
      {text && (
        <motion.p
          className="text-base-content/70 text-sm font-medium"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          {text}
        </motion.p>
      )}
    </div>
  );

  if (fullScreen) {
    return (
      <motion.div
        className="fixed inset-0 bg-base-100/80 backdrop-blur-sm z-50 flex items-center justify-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.2 }}
      >
        {content}
      </motion.div>
    );
  }

  return content;
};

// Skeleton loader component for content placeholders
export const SkeletonLoader = ({ 
  lines = 3, 
  className = '',
  height = 'h-4',
  animated = true 
}) => {
  return (
    <div className={`space-y-3 ${className}`}>
      {Array.from({ length: lines }).map((_, index) => (
        <motion.div
          key={index}
          className={`
            bg-base-300 rounded ${height}
            ${animated ? 'animate-pulse' : ''}
            ${index === lines - 1 ? 'w-3/4' : 'w-full'}
          `}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        />
      ))}
    </div>
  );
};

// Card skeleton for loading cards
export const CardSkeleton = ({ className = '' }) => {
  return (
    <div className={`card-custom p-6 ${className}`}>
      <div className="animate-pulse">
        <div className="bg-base-300 h-48 rounded-lg mb-4"></div>
        <div className="bg-base-300 h-6 rounded mb-2"></div>
        <div className="bg-base-300 h-4 rounded w-3/4 mb-4"></div>
        <div className="flex justify-between items-center">
          <div className="bg-base-300 h-4 rounded w-1/4"></div>
          <div className="bg-base-300 h-8 rounded w-20"></div>
        </div>
      </div>
    </div>
  );
};

// Table skeleton for loading tables
export const TableSkeleton = ({ rows = 5, columns = 4, className = '' }) => {
  return (
    <div className={`overflow-x-auto ${className}`}>
      <table className="table w-full">
        <thead>
          <tr>
            {Array.from({ length: columns }).map((_, index) => (
              <th key={index}>
                <div className="bg-base-300 h-4 rounded animate-pulse"></div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {Array.from({ length: rows }).map((_, rowIndex) => (
            <tr key={rowIndex}>
              {Array.from({ length: columns }).map((_, colIndex) => (
                <td key={colIndex}>
                  <div 
                    className="bg-base-300 h-4 rounded animate-pulse"
                    style={{ animationDelay: `${(rowIndex * columns + colIndex) * 0.1}s` }}
                  ></div>
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default LoadingSpinner;
