import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faBars, 
  faTimes, 
  faUser, 
  faSignOutAlt, 
  faCog, 
  faBell,
  faMoon,
  faSun,
  faGraduationCap
} from '@fortawesome/free-solid-svg-icons';

const Navbar = ({ 
  user = null, 
  onToggleSidebar, 
  onLogout,
  notifications = [],
  className = '' 
}) => {
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const [theme, setTheme] = useState('light');

  // Theme toggle functionality
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);
    document.documentElement.setAttribute('data-theme', savedTheme);
  }, []);

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    localStorage.setItem('theme', newTheme);
    document.documentElement.setAttribute('data-theme', newTheme);
  };

  const handleProfileToggle = () => {
    setIsProfileOpen(!isProfileOpen);
    setIsNotificationsOpen(false);
  };

  const handleNotificationsToggle = () => {
    setIsNotificationsOpen(!isNotificationsOpen);
    setIsProfileOpen(false);
  };

  const unreadNotifications = notifications.filter(n => !n.read).length;

  return (
    <motion.nav
      className={`navbar navbar-custom sticky top-0 z-40 px-4 ${className}`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="navbar-start">
        {/* Sidebar toggle button */}
        <button
          className="btn btn-ghost btn-circle lg:hidden"
          onClick={onToggleSidebar}
        >
          <FontAwesomeIcon icon={faBars} className="text-lg" />
        </button>

        {/* Logo and brand */}
        <motion.div
          className="flex items-center space-x-3 ml-2"
          whileHover={{ scale: 1.05 }}
          transition={{ duration: 0.2 }}
        >
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <FontAwesomeIcon icon={faGraduationCap} className="text-white text-lg" />
          </div>
          <div className="hidden sm:block">
            <h1 className="text-xl font-bold text-primary">EduPlatform</h1>
            <p className="text-xs text-base-content/60">Learning Management System</p>
          </div>
        </motion.div>
      </div>

      <div className="navbar-center">
        {/* Search bar - can be added later */}
        <div className="hidden md:flex">
          {/* Search functionality can be implemented here */}
        </div>
      </div>

      <div className="navbar-end space-x-2">
        {/* Theme toggle */}
        <motion.button
          className="btn btn-ghost btn-circle"
          onClick={toggleTheme}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
        >
          <FontAwesomeIcon 
            icon={theme === 'light' ? faMoon : faSun} 
            className="text-lg" 
          />
        </motion.button>

        {/* Notifications */}
        {user && (
          <div className="dropdown dropdown-end">
            <motion.button
              className="btn btn-ghost btn-circle indicator"
              onClick={handleNotificationsToggle}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <FontAwesomeIcon icon={faBell} className="text-lg" />
              {unreadNotifications > 0 && (
                <span className="badge badge-sm badge-primary indicator-item">
                  {unreadNotifications}
                </span>
              )}
            </motion.button>

            {isNotificationsOpen && (
              <motion.div
                className="dropdown-content z-50 card card-compact w-80 p-2 shadow-xl bg-base-100 border border-base-300"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
              >
                <div className="card-body">
                  <h3 className="font-bold text-lg">Notifications</h3>
                  <div className="max-h-64 overflow-y-auto">
                    {notifications.length > 0 ? (
                      notifications.slice(0, 5).map((notification, index) => (
                        <div
                          key={index}
                          className={`p-3 rounded-lg mb-2 ${
                            notification.read ? 'bg-base-200' : 'bg-primary/10'
                          }`}
                        >
                          <p className="text-sm font-medium">{notification.title}</p>
                          <p className="text-xs text-base-content/60">{notification.message}</p>
                          <p className="text-xs text-base-content/40 mt-1">{notification.time}</p>
                        </div>
                      ))
                    ) : (
                      <p className="text-center text-base-content/60 py-4">
                        No notifications
                      </p>
                    )}
                  </div>
                  {notifications.length > 5 && (
                    <button className="btn btn-sm btn-primary mt-2">
                      View All
                    </button>
                  )}
                </div>
              </motion.div>
            )}
          </div>
        )}

        {/* User profile dropdown */}
        {user ? (
          <div className="dropdown dropdown-end">
            <motion.button
              className="btn btn-ghost btn-circle avatar"
              onClick={handleProfileToggle}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <div className="w-8 rounded-full">
                {user.avatar ? (
                  <img src={user.avatar} alt={user.name} className="user-avatar" />
                ) : (
                  <div className="bg-primary text-primary-content w-8 h-8 rounded-full flex items-center justify-center">
                    <FontAwesomeIcon icon={faUser} />
                  </div>
                )}
              </div>
            </motion.button>

            {isProfileOpen && (
              <motion.div
                className="dropdown-content z-50 menu p-2 shadow-xl bg-base-100 rounded-box w-52 border border-base-300"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
              >
                <div className="px-4 py-3 border-b border-base-300">
                  <p className="text-sm font-medium">{user.name}</p>
                  <p className="text-xs text-base-content/60">{user.email}</p>
                  <span className={`badge badge-sm mt-1 ${
                    user.role === 'admin' ? 'badge-error' :
                    user.role === 'professor' ? 'badge-warning' : 'badge-info'
                  }`}>
                    {user.role}
                  </span>
                </div>
                
                <li>
                  <a className="flex items-center">
                    <FontAwesomeIcon icon={faUser} className="w-4" />
                    Profile
                  </a>
                </li>
                <li>
                  <a className="flex items-center">
                    <FontAwesomeIcon icon={faCog} className="w-4" />
                    Settings
                  </a>
                </li>
                <li>
                  <a 
                    className="flex items-center text-error hover:bg-error/10"
                    onClick={onLogout}
                  >
                    <FontAwesomeIcon icon={faSignOutAlt} className="w-4" />
                    Logout
                  </a>
                </li>
              </motion.div>
            )}
          </div>
        ) : (
          <motion.button
            className="btn btn-primary btn-sm"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Login
          </motion.button>
        )}
      </div>
    </motion.nav>
  );
};

export default Navbar;
