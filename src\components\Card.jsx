import React from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

const Card = ({
  children,
  title,
  subtitle,
  icon,
  image,
  actions,
  hover = true,
  clickable = false,
  onClick,
  className = '',
  headerClassName = '',
  bodyClassName = '',
  footerClassName = '',
  variant = 'default', // default, stat, course, user
  loading = false
}) => {
  const cardVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    },
    hover: hover ? {
      y: -5,
      boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
      transition: {
        duration: 0.2,
        ease: "easeOut"
      }
    } : {},
    tap: clickable ? { scale: 0.98 } : {}
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'stat':
        return 'stat-card';
      case 'course':
        return 'course-card';
      case 'user':
        return 'bg-base-100 border border-base-300 rounded-lg shadow-sm';
      default:
        return 'card-custom';
    }
  };

  const CardContent = () => (
    <>
      {/* Card Header */}
      {(title || subtitle || icon || image) && (
        <div className={`card-header ${headerClassName}`}>
          {image && (
            <div className="mb-4">
              <img 
                src={image} 
                alt={title || 'Card image'} 
                className="w-full h-48 object-cover rounded-lg"
              />
            </div>
          )}
          
          {(title || subtitle || icon) && (
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-3">
                {icon && (
                  <div className="flex-shrink-0">
                    {typeof icon === 'string' ? (
                      <FontAwesomeIcon icon={icon} className="text-primary text-xl" />
                    ) : React.isValidElement(icon) ? (
                      icon
                    ) : (
                      <FontAwesomeIcon icon={icon} className="text-primary text-xl" />
                    )}
                  </div>
                )}
                <div>
                  {title && (
                    <h3 className="text-lg font-semibold text-base-content">
                      {title}
                    </h3>
                  )}
                  {subtitle && (
                    <p className="text-sm text-base-content/60 mt-1">
                      {subtitle}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Card Body */}
      {children && (
        <div className={`card-body ${bodyClassName}`}>
          {loading ? (
            <div className="space-y-3">
              <div className="animate-pulse">
                <div className="h-4 bg-base-300 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-base-300 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-base-300 rounded w-5/6"></div>
              </div>
            </div>
          ) : (
            children
          )}
        </div>
      )}

      {/* Card Footer */}
      {actions && (
        <div className={`card-footer border-t border-base-300 pt-4 ${footerClassName}`}>
          <div className="flex items-center justify-end space-x-2">
            {Array.isArray(actions) ? (
              actions.map((action, index) => (
                <motion.button
                  key={index}
                  className={action.className || 'btn btn-sm btn-primary'}
                  onClick={action.onClick}
                  disabled={action.disabled}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {action.icon && (
                    <FontAwesomeIcon icon={action.icon} className="mr-2" />
                  )}
                  {action.label}
                </motion.button>
              ))
            ) : (
              actions
            )}
          </div>
        </div>
      )}
    </>
  );

  return (
    <motion.div
      className={`${getVariantClasses()} ${clickable ? 'cursor-pointer' : ''} ${className}`}
      variants={cardVariants}
      initial="initial"
      animate="animate"
      whileHover="hover"
      whileTap="tap"
      onClick={clickable ? onClick : undefined}
    >
      <CardContent />
    </motion.div>
  );
};

// Specialized card components
export const StatCard = ({ 
  title, 
  value, 
  icon, 
  change, 
  changeType = 'positive',
  className = '' 
}) => {
  const changeColor = changeType === 'positive' ? 'text-success' : 'text-error';
  const changeIcon = changeType === 'positive' ? '↗' : '↘';

  return (
    <Card
      variant="stat"
      className={className}
      icon={icon}
    >
      <div className="text-center">
        <div className="text-3xl font-bold mb-2">{value}</div>
        <div className="text-sm opacity-90 mb-2">{title}</div>
        {change && (
          <div className={`text-xs ${changeColor} flex items-center justify-center`}>
            <span className="mr-1">{changeIcon}</span>
            {change}
          </div>
        )}
      </div>
    </Card>
  );
};

export const CourseCard = ({ 
  course, 
  onEnroll, 
  onView, 
  enrolled = false,
  className = '' 
}) => {
  const actions = [
    enrolled ? {
      label: 'Continue',
      onClick: () => onView(course),
      className: 'btn btn-sm btn-primary'
    } : {
      label: 'Enroll',
      onClick: () => onEnroll(course),
      className: 'btn btn-sm btn-outline btn-primary'
    }
  ];

  return (
    <Card
      variant="course"
      title={course.title}
      subtitle={`By ${course.instructor}`}
      image={course.image}
      actions={actions}
      clickable
      onClick={() => onView(course)}
      className={className}
    >
      <p className="text-sm text-base-content/70 mb-4">
        {course.description}
      </p>
      <div className="flex items-center justify-between text-xs text-base-content/60">
        <span>{course.duration}</span>
        <span>{course.students} students</span>
        <span className="badge badge-sm badge-primary">{course.level}</span>
      </div>
    </Card>
  );
};

export const UserCard = ({ 
  user, 
  onEdit, 
  onDelete, 
  onView,
  className = '' 
}) => {
  const actions = [
    {
      label: 'View',
      onClick: () => onView(user),
      className: 'btn btn-sm btn-ghost'
    },
    {
      label: 'Edit',
      onClick: () => onEdit(user),
      className: 'btn btn-sm btn-primary'
    },
    {
      label: 'Delete',
      onClick: () => onDelete(user),
      className: 'btn btn-sm btn-error'
    }
  ];

  return (
    <Card
      variant="user"
      actions={actions}
      className={className}
    >
      <div className="flex items-center space-x-4">
        <div className="avatar">
          <div className="w-12 h-12 rounded-full">
            {user.avatar ? (
              <img src={user.avatar} alt={user.name} />
            ) : (
              <div className="bg-primary text-primary-content flex items-center justify-center">
                {user.name.charAt(0).toUpperCase()}
              </div>
            )}
          </div>
        </div>
        <div className="flex-1">
          <h4 className="font-semibold">{user.name}</h4>
          <p className="text-sm text-base-content/60">{user.email}</p>
          <span className={`badge badge-sm mt-1 ${
            user.role === 'admin' ? 'badge-error' :
            user.role === 'professor' ? 'badge-warning' : 'badge-info'
          }`}>
            {user.role}
          </span>
        </div>
      </div>
    </Card>
  );
};

export default Card;
