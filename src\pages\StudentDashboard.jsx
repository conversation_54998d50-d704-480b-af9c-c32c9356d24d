import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faBook,
  faQuestionCircle,
  faTrophy,
  faChartLine,
  faPlay,
  faCheck,
  faClock,
  faGraduationCap,
  faStar,
  faBookOpen,
  faAward,
  faBullseye
} from '@fortawesome/free-solid-svg-icons';
import { toast } from 'react-toastify';

// Import components
import { StatCard, CourseCard } from '../components/Card';
import ProgressChart, { CircularProgress, SimpleProgressBar, PieProgressChart } from '../components/ProgressChart';
import LoadingSpinner, { CardSkeleton } from '../components/LoadingSpinner';

const StudentDashboard = () => {
  const [enrolledCourses, setEnrolledCourses] = useState([]);
  const [availableCourses, setAvailableCourses] = useState([]);
  const [quizzes, setQuizzes] = useState([]);
  const [achievements, setAchievements] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data
  const mockStats = {
    enrolledCourses: 6,
    completedCourses: 3,
    totalQuizzes: 18,
    averageScore: 87
  };

  const mockProgressData = [
    { name: 'Completed', value: 3, color: '#10b981' },
    { name: 'In Progress', value: 3, color: '#3b82f6' },
    { name: 'Not Started', value: 2, color: '#e5e7eb' }
  ];

  const mockEnrolledCourses = [
    {
      id: 1,
      title: 'Introduction to Mathematics',
      description: 'Basic mathematical concepts and problem-solving techniques',
      instructor: 'Prof. Johnson',
      progress: 85,
      duration: '12 weeks',
      level: 'Beginner',
      image: 'https://via.placeholder.com/300x200?text=Math+101',
      status: 'in-progress',
      nextLesson: 'Algebra Basics',
      rating: 4.8
    },
    {
      id: 2,
      title: 'Web Development Fundamentals',
      description: 'Learn HTML, CSS, and JavaScript from scratch',
      instructor: 'Prof. Smith',
      progress: 60,
      duration: '16 weeks',
      level: 'Beginner',
      image: 'https://via.placeholder.com/300x200?text=Web+Dev',
      status: 'in-progress',
      nextLesson: 'JavaScript Functions',
      rating: 4.9
    },
    {
      id: 3,
      title: 'Data Science Basics',
      description: 'Introduction to data analysis and visualization',
      instructor: 'Prof. Davis',
      progress: 100,
      duration: '10 weeks',
      level: 'Intermediate',
      image: 'https://via.placeholder.com/300x200?text=Data+Science',
      status: 'completed',
      nextLesson: null,
      rating: 4.7
    }
  ];

  const mockAvailableCourses = [
    {
      id: 4,
      title: 'Advanced Python Programming',
      description: 'Deep dive into Python programming concepts',
      instructor: 'Prof. Wilson',
      students: 234,
      duration: '14 weeks',
      level: 'Advanced',
      image: 'https://via.placeholder.com/300x200?text=Python',
      rating: 4.6,
      price: 'Free'
    },
    {
      id: 5,
      title: 'Machine Learning Basics',
      description: 'Introduction to ML algorithms and applications',
      instructor: 'Prof. Brown',
      students: 189,
      duration: '12 weeks',
      level: 'Intermediate',
      image: 'https://via.placeholder.com/300x200?text=ML',
      rating: 4.8,
      price: 'Free'
    }
  ];

  const mockQuizzes = [
    {
      id: 1,
      title: 'Algebra Fundamentals Quiz',
      course: 'Introduction to Mathematics',
      questions: 15,
      duration: 30,
      dueDate: '2024-07-20',
      status: 'pending',
      attempts: 0,
      maxAttempts: 3
    },
    {
      id: 2,
      title: 'JavaScript Basics Test',
      course: 'Web Development Fundamentals',
      questions: 20,
      duration: 45,
      dueDate: '2024-07-22',
      status: 'pending',
      attempts: 1,
      maxAttempts: 2,
      lastScore: 78
    },
    {
      id: 3,
      title: 'Data Visualization Quiz',
      course: 'Data Science Basics',
      questions: 12,
      duration: 25,
      dueDate: '2024-07-15',
      status: 'completed',
      attempts: 2,
      maxAttempts: 3,
      lastScore: 92
    }
  ];

  const mockAchievements = [
    {
      id: 1,
      title: 'First Course Completed',
      description: 'Completed your first course',
      icon: faGraduationCap,
      earned: true,
      date: '2024-06-15'
    },
    {
      id: 2,
      title: 'Quiz Master',
      description: 'Scored 90+ on 5 quizzes',
      icon: faTrophy,
      earned: true,
      date: '2024-07-01'
    },
    {
      id: 3,
      title: 'Consistent Learner',
      description: 'Studied for 7 consecutive days',
      icon: faBullseye,
      earned: false,
      progress: 5
    }
  ];

  // Load data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      setEnrolledCourses(mockEnrolledCourses);
      setAvailableCourses(mockAvailableCourses);
      setQuizzes(mockQuizzes);
      setAchievements(mockAchievements);
      setIsLoading(false);
    };

    loadData();
  }, []);

  // Actions
  const handleContinueCourse = (course) => {
    toast.info(`Continuing ${course.title} - ${course.nextLesson}`);
  };

  const handleEnrollCourse = (course) => {
    toast.success(`Enrolled in ${course.title}!`);
    // Move course from available to enrolled
    setAvailableCourses(prev => prev.filter(c => c.id !== course.id));
    setEnrolledCourses(prev => [...prev, { ...course, progress: 0, status: 'in-progress' }]);
  };

  const handleTakeQuiz = (quiz) => {
    toast.info(`Starting ${quiz.title}`);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="stat-card animate-pulse">
              <div className="h-16 bg-base-300 rounded"></div>
            </div>
          ))}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <CardSkeleton key={i} />
          ))}
        </div>
      </div>
    );
  }

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header */}
      <motion.div variants={itemVariants}>
        <h1 className="text-3xl font-bold text-base-content mb-2">Student Dashboard</h1>
        <p className="text-base-content/60">Track your learning progress and discover new courses</p>
      </motion.div>

      {/* Stats Cards */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        variants={itemVariants}
        data-aos="fade-up"
        data-aos-delay="100"
      >
        <StatCard
          title="Enrolled Courses"
          value={mockStats.enrolledCourses}
          icon={faBook}
          change="+2"
          changeType="positive"
        />
        <StatCard
          title="Completed"
          value={mockStats.completedCourses}
          icon={faCheck}
          change="+1"
          changeType="positive"
        />
        <StatCard
          title="Quizzes Taken"
          value={mockStats.totalQuizzes}
          icon={faQuestionCircle}
          change="+3"
          changeType="positive"
        />
        <StatCard
          title="Average Score"
          value={`${mockStats.averageScore}%`}
          icon={faChartLine}
          change="+5%"
          changeType="positive"
        />
      </motion.div>

      {/* Progress Overview */}
      <motion.div
        className="grid grid-cols-1 lg:grid-cols-3 gap-6"
        variants={itemVariants}
        data-aos="fade-up"
        data-aos-delay="200"
      >
        <div className="lg:col-span-2">
          <div className="bg-base-100 rounded-xl shadow-md p-6 border border-base-300">
            <h3 className="text-xl font-semibold mb-4">Learning Progress</h3>
            <div className="space-y-4">
              {enrolledCourses.map((course) => (
                <div key={course.id} className="flex items-center space-x-4">
                  <img 
                    src={course.image} 
                    alt={course.title}
                    className="w-12 h-12 rounded-lg object-cover"
                  />
                  <div className="flex-1">
                    <h4 className="font-medium">{course.title}</h4>
                    <SimpleProgressBar
                      value={course.progress}
                      max={100}
                      size="sm"
                      showPercentage={true}
                      className="mt-1"
                    />
                  </div>
                  <div className="text-right">
                    <span className="text-sm font-medium">{course.progress}%</span>
                    <p className="text-xs text-base-content/60">
                      {course.status === 'completed' ? 'Completed' : course.nextLesson}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="space-y-6">
          {/* Overall Progress */}
          <div className="bg-base-100 rounded-xl shadow-md p-6 border border-base-300">
            <h3 className="text-lg font-semibold mb-4">Overall Progress</h3>
            <div className="flex justify-center">
              <CircularProgress
                value={75}
                max={100}
                size={120}
                color="#3b82f6"
                label="Course Completion"
              />
            </div>
          </div>

          {/* Course Distribution */}
          <div className="bg-base-100 rounded-xl shadow-md p-6 border border-base-300">
            <h3 className="text-lg font-semibold mb-4">Course Status</h3>
            <PieProgressChart
              data={mockProgressData}
              height={200}
              showLegend={false}
              showTooltip={true}
            />
          </div>
        </div>
      </motion.div>

      {/* Tabs */}
      <motion.div variants={itemVariants}>
        <div className="tabs tabs-boxed bg-base-200 p-1">
          <button 
            className={`tab ${activeTab === 'overview' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </button>
          <button 
            className={`tab ${activeTab === 'courses' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('courses')}
          >
            My Courses
          </button>
          <button 
            className={`tab ${activeTab === 'quizzes' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('quizzes')}
          >
            Quizzes
          </button>
          <button 
            className={`tab ${activeTab === 'discover' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('discover')}
          >
            Discover
          </button>
          <button 
            className={`tab ${activeTab === 'achievements' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('achievements')}
          >
            Achievements
          </button>
        </div>
      </motion.div>

      {/* Tab Content */}
      <motion.div variants={itemVariants}>
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Continue Learning */}
            <div>
              <h3 className="text-xl font-semibold mb-4">Continue Learning</h3>
              <div className="space-y-4">
                {enrolledCourses.filter(c => c.status === 'in-progress').map((course) => (
                  <div key={course.id} className="card-custom p-4">
                    <div className="flex items-center space-x-4">
                      <img 
                        src={course.image} 
                        alt={course.title}
                        className="w-16 h-16 rounded-lg object-cover"
                      />
                      <div className="flex-1">
                        <h4 className="font-semibold">{course.title}</h4>
                        <p className="text-sm text-base-content/60 mb-2">
                          Next: {course.nextLesson}
                        </p>
                        <SimpleProgressBar
                          value={course.progress}
                          max={100}
                          size="sm"
                          showPercentage={true}
                        />
                      </div>
                      <button 
                        className="btn btn-primary btn-sm"
                        onClick={() => handleContinueCourse(course)}
                      >
                        <FontAwesomeIcon icon={faPlay} className="mr-1" />
                        Continue
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Upcoming Quizzes */}
            <div>
              <h3 className="text-xl font-semibold mb-4">Upcoming Quizzes</h3>
              <div className="space-y-4">
                {quizzes.filter(q => q.status === 'pending').map((quiz) => (
                  <div key={quiz.id} className="card-custom p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold">{quiz.title}</h4>
                        <p className="text-sm text-base-content/60">{quiz.course}</p>
                        <div className="flex items-center space-x-4 text-xs text-base-content/60 mt-1">
                          <span>{quiz.questions} questions</span>
                          <span>
                            <FontAwesomeIcon icon={faClock} className="mr-1" />
                            {quiz.duration}min
                          </span>
                          <span>Due: {quiz.dueDate}</span>
                        </div>
                      </div>
                      <button 
                        className="btn btn-primary btn-sm"
                        onClick={() => handleTakeQuiz(quiz)}
                      >
                        Take Quiz
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'courses' && (
          <div>
            <h3 className="text-xl font-semibold mb-6">My Enrolled Courses</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {enrolledCourses.map((course) => (
                <div key={course.id} className="card-custom">
                  <img 
                    src={course.image} 
                    alt={course.title}
                    className="w-full h-48 object-cover rounded-lg mb-4"
                  />
                  <h4 className="text-lg font-semibold mb-2">{course.title}</h4>
                  <p className="text-base-content/60 text-sm mb-4">{course.description}</p>
                  
                  <div className="flex items-center justify-between text-sm text-base-content/60 mb-4">
                    <span>By {course.instructor}</span>
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faStar} className="text-warning mr-1" />
                      {course.rating}
                    </div>
                  </div>

                  <div className="mb-4">
                    <div className="flex justify-between text-sm mb-1">
                      <span>Progress</span>
                      <span>{course.progress}%</span>
                    </div>
                    <SimpleProgressBar
                      value={course.progress}
                      max={100}
                      size="sm"
                      showPercentage={false}
                    />
                  </div>

                  <button 
                    className={`btn w-full ${
                      course.status === 'completed' ? 'btn-success' : 'btn-primary'
                    }`}
                    onClick={() => handleContinueCourse(course)}
                  >
                    {course.status === 'completed' ? (
                      <>
                        <FontAwesomeIcon icon={faCheck} className="mr-2" />
                        Completed
                      </>
                    ) : (
                      <>
                        <FontAwesomeIcon icon={faPlay} className="mr-2" />
                        Continue
                      </>
                    )}
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'quizzes' && (
          <div>
            <h3 className="text-xl font-semibold mb-6">Quiz Center</h3>
            <div className="overflow-x-auto">
              <table className="table w-full">
                <thead>
                  <tr>
                    <th>Quiz Title</th>
                    <th>Course</th>
                    <th>Questions</th>
                    <th>Duration</th>
                    <th>Due Date</th>
                    <th>Attempts</th>
                    <th>Best Score</th>
                    <th>Status</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {quizzes.map((quiz) => (
                    <tr key={quiz.id}>
                      <td className="font-medium">{quiz.title}</td>
                      <td>{quiz.course}</td>
                      <td>{quiz.questions}</td>
                      <td>{quiz.duration}min</td>
                      <td>{quiz.dueDate}</td>
                      <td>{quiz.attempts}/{quiz.maxAttempts}</td>
                      <td>{quiz.lastScore || '-'}%</td>
                      <td>
                        <span className={`badge ${
                          quiz.status === 'completed' ? 'badge-success' : 
                          quiz.status === 'pending' ? 'badge-warning' : 'badge-error'
                        }`}>
                          {quiz.status}
                        </span>
                      </td>
                      <td>
                        {quiz.status === 'pending' && quiz.attempts < quiz.maxAttempts && (
                          <button 
                            className="btn btn-sm btn-primary"
                            onClick={() => handleTakeQuiz(quiz)}
                          >
                            Take Quiz
                          </button>
                        )}
                        {quiz.status === 'completed' && (
                          <button className="btn btn-sm btn-ghost">
                            View Results
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === 'discover' && (
          <div>
            <h3 className="text-xl font-semibold mb-6">Discover New Courses</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {availableCourses.map((course) => (
                <CourseCard
                  key={course.id}
                  course={course}
                  onView={() => toast.info(`Viewing ${course.title}`)}
                  onEnroll={handleEnrollCourse}
                  enrolled={false}
                />
              ))}
            </div>
          </div>
        )}

        {activeTab === 'achievements' && (
          <div>
            <h3 className="text-xl font-semibold mb-6">Achievements & Badges</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {achievements.map((achievement) => (
                <div 
                  key={achievement.id} 
                  className={`card-custom p-6 text-center ${
                    achievement.earned ? 'border-success' : 'opacity-60'
                  }`}
                >
                  <div className={`w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center ${
                    achievement.earned ? 'bg-success text-success-content' : 'bg-base-300'
                  }`}>
                    <FontAwesomeIcon icon={achievement.icon} className="text-2xl" />
                  </div>
                  <h4 className="font-semibold mb-2">{achievement.title}</h4>
                  <p className="text-sm text-base-content/60 mb-4">{achievement.description}</p>
                  
                  {achievement.earned ? (
                    <div className="text-success text-sm">
                      <FontAwesomeIcon icon={faCheck} className="mr-1" />
                      Earned on {achievement.date}
                    </div>
                  ) : achievement.progress ? (
                    <div>
                      <SimpleProgressBar
                        value={achievement.progress}
                        max={7}
                        label={`${achievement.progress}/7 days`}
                        size="sm"
                      />
                    </div>
                  ) : (
                    <div className="text-base-content/60 text-sm">Not earned yet</div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </motion.div>
    </motion.div>
  );
};

export default StudentDashboard;
