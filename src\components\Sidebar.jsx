import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faTachometerAlt,
  faUsers,
  faChalkboardTeacher,
  faGraduationCap,
  faBook,
  faQuestionCircle,
  faChartBar,
  faCog,
  faFileUpload,
  faClipboardList,
  faTrophy,
  faBookOpen,
  faUserGraduate
} from '@fortawesome/free-solid-svg-icons';

const Sidebar = ({ 
  isOpen = true, 
  userRole = 'student', 
  activeItem = 'dashboard',
  onItemClick,
  onClose,
  className = '' 
}) => {
  // Navigation items based on user role
  const getNavigationItems = () => {
    const commonItems = [
      { id: 'dashboard', label: 'Dashboard', icon: faTachometerAlt, path: '/dashboard' }
    ];

    const roleSpecificItems = {
      admin: [
        { id: 'users', label: 'User Management', icon: faUsers, path: '/admin/users' },
        { id: 'courses', label: 'Course Management', icon: faBook, path: '/admin/courses' },
        { id: 'analytics', label: 'Analytics', icon: faChartBar, path: '/admin/analytics' },
        { id: 'settings', label: 'System Settings', icon: faCog, path: '/admin/settings' }
      ],
      professor: [
        { id: 'courses', label: 'My Courses', icon: faChalkboardTeacher, path: '/professor/courses' },
        { id: 'upload', label: 'Upload Lecture', icon: faFileUpload, path: '/professor/upload' },
        { id: 'quizzes', label: 'Manage Quizzes', icon: faQuestionCircle, path: '/professor/quizzes' },
        { id: 'students', label: 'Student Performance', icon: faUserGraduate, path: '/professor/students' },
        { id: 'analytics', label: 'Course Analytics', icon: faChartBar, path: '/professor/analytics' }
      ],
      student: [
        { id: 'courses', label: 'My Courses', icon: faBookOpen, path: '/student/courses' },
        { id: 'quizzes', label: 'Take Quizzes', icon: faClipboardList, path: '/student/quizzes' },
        { id: 'progress', label: 'My Progress', icon: faTrophy, path: '/student/progress' },
        { id: 'recommendations', label: 'Recommendations', icon: faGraduationCap, path: '/student/recommendations' }
      ]
    };

    return [...commonItems, ...roleSpecificItems[userRole]];
  };

  const navigationItems = getNavigationItems();

  const handleItemClick = (item) => {
    if (onItemClick) {
      onItemClick(item);
    }
    // Close sidebar on mobile after item click
    if (window.innerWidth < 1024 && onClose) {
      onClose();
    }
  };

  const sidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      x: -280,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  };

  const itemVariants = {
    open: {
      opacity: 1,
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      opacity: 0,
      x: -20,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  };

  return (
    <>
      {/* Mobile overlay */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.aside
        className={`
          fixed top-0 left-0 z-50 h-full w-70 bg-base-100 border-r border-base-300 shadow-lg
          lg:relative lg:translate-x-0 lg:shadow-none
          ${className}
        `}
        variants={sidebarVariants}
        animate={isOpen ? "open" : "closed"}
        initial="closed"
      >
        <div className="flex flex-col h-full">
          {/* Sidebar header */}
          <motion.div
            className="flex items-center justify-between p-4 border-b border-base-300"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.1 }}
          >
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <FontAwesomeIcon icon={faGraduationCap} className="text-white text-sm" />
              </div>
              <div>
                <h2 className="text-lg font-bold text-primary">EduPlatform</h2>
                <p className="text-xs text-base-content/60 capitalize">{userRole} Panel</p>
              </div>
            </div>
            
            {/* Close button for mobile */}
            <button
              className="btn btn-ghost btn-sm btn-circle lg:hidden"
              onClick={onClose}
            >
              <FontAwesomeIcon icon={faTimes} />
            </button>
          </motion.div>

          {/* Navigation */}
          <nav className="flex-1 overflow-y-auto p-4">
            <ul className="space-y-2">
              {navigationItems.map((item, index) => (
                <motion.li
                  key={item.id}
                  variants={itemVariants}
                  initial="closed"
                  animate="open"
                  transition={{ delay: index * 0.1 }}
                >
                  <button
                    className={`
                      sidebar-item w-full text-left
                      ${activeItem === item.id ? 'active' : ''}
                    `}
                    onClick={() => handleItemClick(item)}
                  >
                    <FontAwesomeIcon icon={item.icon} className="w-5 h-5 mr-3" />
                    <span className="font-medium">{item.label}</span>
                  </button>
                </motion.li>
              ))}
            </ul>
          </nav>

          {/* Sidebar footer */}
          <motion.div
            className="p-4 border-t border-base-300"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <div className="text-center">
              <p className="text-xs text-base-content/60">
                © 2025 EduPlatform
              </p>
              <p className="text-xs text-base-content/40">
                Version 1.0.0
              </p>
            </div>
          </motion.div>
        </div>
      </motion.aside>
    </>
  );
};

export default Sidebar;
