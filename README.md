# EduPlatform - Professional Learning Management System

A modern, responsive, role-based Learning Management System (LMS) built with React and cutting-edge web technologies. Designed for universities, e-learning startups, and enterprise-grade clients.

![EduPlatform](https://via.placeholder.com/800x400?text=EduPlatform+LMS)

## 🌟 Features

### 🔐 Authentication & Authorization
- **Role-based access control** (<PERSON><PERSON>, Professor, Student)
- **Secure login/registration** with form validation
- **Protected routes** with automatic role-based redirects
- **Session management** with persistent authentication

### 👨‍💼 Admin Dashboard
- **User Management** - Complete CRUD operations for users
- **System Analytics** - Visual charts and statistics
- **Course Management** - Oversee all courses and content
- **Real-time Monitoring** - Track system performance and usage

### 👨‍🏫 Professor Dashboard
- **Course Creation** - Build and manage courses
- **Lecture Upload** - Upload and organize course materials
- **Quiz Management** - Create, edit, and monitor quizzes
- **Student Performance** - Track and analyze student progress
- **Analytics** - Detailed course and engagement metrics

### 👨‍🎓 Student Dashboard
- **Course Enrollment** - Browse and enroll in available courses
- **Progress Tracking** - Visual progress indicators and completion status
- **Quiz Taking** - Interactive quiz interface with scoring
- **Achievements** - Gamified learning with badges and rewards
- **Personalized Recommendations** - AI-driven course suggestions

## 🛠️ Tech Stack

### ⚛️ Core Framework
- **React 19** - Latest React with functional components and Hooks
- **Vite** - Lightning-fast build tool and development server
- **React Router DOM** - Client-side routing with protected routes

### 🎨 Styling & UI
- **Tailwind CSS** - Utility-first CSS framework
- **DaisyUI** - Beautiful component library for Tailwind
- **Bootstrap 5** - Grid system and form components
- **SCSS** - Enhanced CSS with variables and mixins
- **Font Awesome** - Comprehensive icon library

### 🌀 Animations & UX
- **Framer Motion** - Smooth animations and page transitions
- **AOS (Animate On Scroll)** - Scroll-triggered animations
- **React Toastify** - Elegant toast notifications
- **Loading States** - Skeleton loaders and spinners

### 📊 Data Visualization
- **Recharts** - Beautiful and responsive charts
- **Progress Indicators** - Circular and linear progress bars
- **Analytics Dashboards** - Interactive data visualization

### 🎯 Additional Features
- **Dark Mode Support** - Multiple theme options with DaisyUI
- **Responsive Design** - Mobile-first approach
- **Accessibility** - ARIA labels and keyboard navigation
- **TypeScript Ready** - Easy migration to TypeScript
- **PWA Ready** - Progressive Web App capabilities

## 📁 Project Structure

```
src/
├── assets/
│   └── lottie/              # Lottie animations
├── components/
│   ├── AlertModal.jsx       # Reusable modal components
│   ├── Card.jsx            # Flexible card components
│   ├── LoadingSpinner.jsx  # Loading states and skeletons
│   ├── Navbar.jsx          # Navigation bar with theme switcher
│   ├── PageTransition.jsx  # Page transition animations
│   ├── ProgressChart.jsx   # Chart components for analytics
│   └── Sidebar.jsx         # Role-based sidebar navigation
├── contexts/
│   ├── AuthContext.jsx     # Authentication state management
│   └── ThemeContext.jsx    # Theme and dark mode management
├── pages/
│   ├── AdminDashboard.jsx  # Admin dashboard with user management
│   ├── Login.jsx           # Login page with validation
│   ├── ProfessorDashboard.jsx # Professor course management
│   ├── Register.jsx        # Registration with role selection
│   └── StudentDashboard.jsx   # Student learning interface
├── styles/
│   ├── global.scss         # Global SCSS variables and mixins
│   ├── overrides.scss      # Component-specific overrides
│   └── index.css           # Main CSS with imports
├── App.jsx                 # Main app component with layout
├── main.jsx               # App entry point with providers
└── router.jsx             # Route configuration
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd lms-frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

### Demo Credentials

For testing purposes, use these demo credentials:

| Role | Email | Password |
|------|-------|----------|
| Admin | <EMAIL> | password123 |
| Professor | <EMAIL> | password123 |
| Student | <EMAIL> | password123 |

## 🎨 Themes

EduPlatform supports 20+ beautiful themes:
- Light & Dark modes
- Cupcake, Bumblebee, Emerald
- Corporate, Synthwave, Retro
- Cyberpunk, Valentine, Halloween
- And many more!

Access the theme selector from the navbar palette icon.

## 📱 Responsive Design

- **Mobile First** - Optimized for mobile devices
- **Tablet Support** - Enhanced experience on tablets
- **Desktop** - Full-featured desktop interface
- **Breakpoints** - Tailwind CSS responsive breakpoints

## ♿ Accessibility

- **ARIA Labels** - Screen reader support
- **Keyboard Navigation** - Full keyboard accessibility
- **Color Contrast** - WCAG compliant color schemes
- **Focus Management** - Proper focus indicators

## 🔧 Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
```

## 🏗️ Build & Deployment

### Production Build
```bash
npm run build
```

### Preview Production Build
```bash
npm run preview
```

The build artifacts will be stored in the `dist/` directory.

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **React Team** - For the amazing React framework
- **Tailwind CSS** - For the utility-first CSS framework
- **DaisyUI** - For beautiful UI components
- **Framer Motion** - For smooth animations
- **Recharts** - For data visualization components

## 📞 Support

For support, email <EMAIL> or join our Slack channel.

---

**Built with ❤️ for the education community**
