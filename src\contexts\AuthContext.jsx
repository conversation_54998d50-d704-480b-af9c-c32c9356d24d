import React, { createContext, useContext, useState, useEffect } from 'react';
import { toast } from 'react-toastify';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Check for stored auth token
        const token = localStorage.getItem('authToken');
        const userData = localStorage.getItem('userData');
        
        if (token && userData) {
          // Validate token with backend (simulated)
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          const parsedUser = JSON.parse(userData);
          setUser(parsedUser);
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        // Clear invalid auth data
        localStorage.removeItem('authToken');
        localStorage.removeItem('userData');
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Login function
  const login = async (credentials) => {
    try {
      setIsLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock authentication logic
      const { email, password } = credentials;
      
      // Demo credentials
      const demoUsers = {
        '<EMAIL>': { role: 'admin', name: 'Admin User' },
        '<EMAIL>': { role: 'professor', name: 'Professor Smith' },
        '<EMAIL>': { role: 'student', name: 'John Student' }
      };
      
      if (demoUsers[email] && password === 'password123') {
        const userData = {
          id: Math.random().toString(36).substr(2, 9),
          email,
          name: demoUsers[email].name,
          role: demoUsers[email].role,
          avatar: null,
          loginTime: new Date().toISOString()
        };
        
        // Store auth data
        const token = 'mock-jwt-token-' + Math.random().toString(36).substr(2, 9);
        localStorage.setItem('authToken', token);
        localStorage.setItem('userData', JSON.stringify(userData));
        
        setUser(userData);
        setIsAuthenticated(true);
        
        return { success: true, user: userData };
      } else {
        throw new Error('Invalid credentials');
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (userData) => {
    try {
      setIsLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock registration logic
      const newUser = {
        id: Math.random().toString(36).substr(2, 9),
        email: userData.email,
        name: `${userData.firstName} ${userData.lastName}`,
        role: userData.role,
        avatar: null,
        registrationTime: new Date().toISOString()
      };
      
      // In a real app, you wouldn't auto-login after registration
      // but for demo purposes, we'll do it
      const token = 'mock-jwt-token-' + Math.random().toString(36).substr(2, 9);
      localStorage.setItem('authToken', token);
      localStorage.setItem('userData', JSON.stringify(newUser));
      
      setUser(newUser);
      setIsAuthenticated(true);
      
      return { success: true, user: newUser };
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setIsLoading(true);
      
      // Simulate API call to invalidate token
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Clear auth data
      localStorage.removeItem('authToken');
      localStorage.removeItem('userData');
      
      setUser(null);
      setIsAuthenticated(false);
      
      toast.success('Logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Error during logout');
    } finally {
      setIsLoading(false);
    }
  };

  // Update user profile
  const updateProfile = async (updates) => {
    try {
      setIsLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const updatedUser = { ...user, ...updates };
      localStorage.setItem('userData', JSON.stringify(updatedUser));
      
      setUser(updatedUser);
      toast.success('Profile updated successfully');
      
      return { success: true, user: updatedUser };
    } catch (error) {
      console.error('Profile update error:', error);
      toast.error('Failed to update profile');
      return { success: false, error: error.message };
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user has specific role
  const hasRole = (role) => {
    return user?.role === role;
  };

  // Check if user has any of the specified roles
  const hasAnyRole = (roles) => {
    return roles.includes(user?.role);
  };

  // Get user's dashboard route based on role
  const getDashboardRoute = () => {
    if (!user) return '/login';
    
    switch (user.role) {
      case 'admin':
        return '/admin/dashboard';
      case 'professor':
        return '/professor/dashboard';
      case 'student':
        return '/student/dashboard';
      default:
        return '/login';
    }
  };

  const value = {
    // State
    user,
    isLoading,
    isAuthenticated,
    
    // Actions
    login,
    register,
    logout,
    updateProfile,
    
    // Utilities
    hasRole,
    hasAnyRole,
    getDashboardRoute
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
