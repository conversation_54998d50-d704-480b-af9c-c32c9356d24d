// Component-specific SCSS overrides
@import './global.scss';

// Bootstrap overrides
.btn {
  @include button-base;
  
  &.btn-primary {
    background-color: $primary-color;
    border-color: $primary-color;
    
    &:hover {
      background-color: darken($primary-color, 10%);
      border-color: darken($primary-color, 10%);
      transform: translateY(-1px);
    }
  }
  
  &.btn-outline-primary {
    color: $primary-color;
    border-color: $primary-color;
    
    &:hover {
      background-color: $primary-color;
      border-color: $primary-color;
      transform: translateY(-1px);
    }
  }
}

.card {
  @include card-base;
  border: 1px solid rgba(0, 0, 0, 0.1);
  
  .card-header {
    background-color: rgba($primary-color, 0.05);
    border-bottom: 1px solid rgba($primary-color, 0.1);
    font-weight: 600;
  }
}

.form-control {
  @include transition-smooth;
  
  &:focus {
    border-color: $primary-color;
    box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
  }
}

.nav-pills {
  .nav-link {
    @include transition-smooth;
    
    &.active {
      background-color: $primary-color;
    }
    
    &:hover:not(.active) {
      background-color: rgba($primary-color, 0.1);
      color: $primary-color;
    }
  }
}

// DaisyUI component overrides
.navbar {
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.9);
  
  [data-theme="dark"] & {
    background-color: rgba(30, 41, 59, 0.9);
  }
}

.drawer-side {
  .menu {
    li {
      a {
        @include transition-smooth;
        
        &:hover {
          background-color: rgba($primary-color, 0.1);
          color: $primary-color;
        }
        
        &.active {
          background-color: $primary-color;
          color: white;
        }
      }
    }
  }
}

.modal {
  .modal-box {
    @include card-base;
    max-width: 90vw;
    
    @include tablet-up {
      max-width: 32rem;
    }
  }
}

.alert {
  @include transition-smooth;
  
  &.alert-success {
    background-color: rgba($success-color, 0.1);
    border-color: $success-color;
    color: darken($success-color, 20%);
  }
  
  &.alert-error {
    background-color: rgba($error-color, 0.1);
    border-color: $error-color;
    color: darken($error-color, 20%);
  }
  
  &.alert-warning {
    background-color: rgba($warning-color, 0.1);
    border-color: $warning-color;
    color: darken($warning-color, 20%);
  }
  
  &.alert-info {
    background-color: rgba($info-color, 0.1);
    border-color: $info-color;
    color: darken($info-color, 20%);
  }
}

.progress {
  height: 8px;
  background-color: rgba($primary-color, 0.1);
  
  .progress-bar {
    background-color: $primary-color;
    @include transition-smooth;
  }
}

// Custom component styles
.dashboard-grid {
  display: grid;
  gap: $spacing-lg;
  
  @include mobile-only {
    grid-template-columns: 1fr;
  }
  
  @include tablet-up {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @include desktop-up {
    grid-template-columns: repeat(3, 1fr);
  }
  
  @include large-desktop-up {
    grid-template-columns: repeat(4, 1fr);
  }
}

.stats-grid {
  display: grid;
  gap: $spacing-md;
  
  @include mobile-only {
    grid-template-columns: 1fr;
  }
  
  @include tablet-up {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @include desktop-up {
    grid-template-columns: repeat(4, 1fr);
  }
}

.course-card {
  @include card-base;
  @include hover-lift;
  cursor: pointer;
  
  .course-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: $border-radius-md;
    margin-bottom: $spacing-md;
  }
  
  .course-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: $spacing-sm;
    color: $primary-color;
  }
  
  .course-description {
    color: $secondary-color;
    margin-bottom: $spacing-md;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .course-meta {
    @include flex-between;
    font-size: 0.875rem;
    color: $secondary-color;
  }
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid $primary-color;
}

.sidebar-nav {
  .nav-item {
    margin-bottom: $spacing-xs;
    
    .nav-link {
      @include flex-center;
      justify-content: flex-start;
      padding: $spacing-md;
      border-radius: $border-radius-md;
      color: inherit;
      text-decoration: none;
      @include transition-smooth;
      
      i {
        margin-right: $spacing-md;
        width: 20px;
        text-align: center;
      }
      
      &:hover {
        background-color: rgba($primary-color, 0.1);
        color: $primary-color;
      }
      
      &.active {
        background-color: $primary-color;
        color: white;
      }
    }
  }
}

// Loading states
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// Print styles
@media print {
  .no-print {
    display: none !important;
  }
  
  .sidebar,
  .navbar,
  .btn,
  .modal {
    display: none !important;
  }
  
  .main-content {
    margin: 0 !important;
    padding: 0 !important;
  }
}
