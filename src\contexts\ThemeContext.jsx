import React, { createContext, useContext, useState, useEffect } from 'react';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState('light');
  const [isLoading, setIsLoading] = useState(true);

  // Available themes
  const themes = {
    light: {
      name: 'Light',
      value: 'light',
      icon: '☀️'
    },
    dark: {
      name: 'Dark',
      value: 'dark',
      icon: '🌙'
    },
    cupcake: {
      name: 'Cupcake',
      value: 'cupcake',
      icon: '🧁'
    },
    bumblebee: {
      name: 'Bumblebee',
      value: 'bumblebee',
      icon: '🐝'
    },
    emerald: {
      name: 'Emerald',
      value: 'emerald',
      icon: '💎'
    },
    corporate: {
      name: 'Corporate',
      value: 'corporate',
      icon: '🏢'
    },
    synthwave: {
      name: 'Synthwave',
      value: 'synthwave',
      icon: '🌆'
    },
    retro: {
      name: 'Retro',
      value: 'retro',
      icon: '📼'
    },
    cyberpunk: {
      name: 'Cyberpunk',
      value: 'cyberpunk',
      icon: '🤖'
    },
    valentine: {
      name: 'Valentine',
      value: 'valentine',
      icon: '💝'
    },
    halloween: {
      name: 'Halloween',
      value: 'halloween',
      icon: '🎃'
    },
    garden: {
      name: 'Garden',
      value: 'garden',
      icon: '🌸'
    },
    forest: {
      name: 'Forest',
      value: 'forest',
      icon: '🌲'
    },
    aqua: {
      name: 'Aqua',
      value: 'aqua',
      icon: '🌊'
    },
    lofi: {
      name: 'Lo-Fi',
      value: 'lofi',
      icon: '🎵'
    },
    pastel: {
      name: 'Pastel',
      value: 'pastel',
      icon: '🎨'
    },
    fantasy: {
      name: 'Fantasy',
      value: 'fantasy',
      icon: '🧚'
    },
    wireframe: {
      name: 'Wireframe',
      value: 'wireframe',
      icon: '📐'
    },
    black: {
      name: 'Black',
      value: 'black',
      icon: '⚫'
    },
    luxury: {
      name: 'Luxury',
      value: 'luxury',
      icon: '👑'
    },
    dracula: {
      name: 'Dracula',
      value: 'dracula',
      icon: '🧛'
    }
  };

  // Initialize theme
  useEffect(() => {
    const initializeTheme = () => {
      try {
        // Check for saved theme preference
        const savedTheme = localStorage.getItem('theme');
        
        // Check for system preference if no saved theme
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        const initialTheme = savedTheme || (systemPrefersDark ? 'dark' : 'light');
        
        setTheme(initialTheme);
        applyTheme(initialTheme);
      } catch (error) {
        console.error('Error initializing theme:', error);
        setTheme('light');
        applyTheme('light');
      } finally {
        setIsLoading(false);
      }
    };

    initializeTheme();
  }, []);

  // Apply theme to document
  const applyTheme = (themeName) => {
    try {
      document.documentElement.setAttribute('data-theme', themeName);
      
      // Update meta theme-color for mobile browsers
      const metaThemeColor = document.querySelector('meta[name="theme-color"]');
      if (metaThemeColor) {
        const isDark = themeName === 'dark' || themeName === 'black' || themeName === 'dracula';
        metaThemeColor.setAttribute('content', isDark ? '#1e293b' : '#ffffff');
      }
    } catch (error) {
      console.error('Error applying theme:', error);
    }
  };

  // Change theme
  const changeTheme = (newTheme) => {
    try {
      if (themes[newTheme]) {
        setTheme(newTheme);
        applyTheme(newTheme);
        localStorage.setItem('theme', newTheme);
      }
    } catch (error) {
      console.error('Error changing theme:', error);
    }
  };

  // Toggle between light and dark
  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    changeTheme(newTheme);
  };

  // Get current theme info
  const getCurrentTheme = () => {
    return themes[theme] || themes.light;
  };

  // Check if current theme is dark
  const isDarkTheme = () => {
    const darkThemes = ['dark', 'black', 'dracula', 'synthwave', 'halloween', 'forest', 'luxury'];
    return darkThemes.includes(theme);
  };

  // Get theme list for selection
  const getThemeList = () => {
    return Object.values(themes);
  };

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleSystemThemeChange = (e) => {
      // Only auto-switch if user hasn't manually selected a theme
      const savedTheme = localStorage.getItem('theme');
      if (!savedTheme) {
        const newTheme = e.matches ? 'dark' : 'light';
        setTheme(newTheme);
        applyTheme(newTheme);
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);
    
    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }, []);

  const value = {
    // State
    theme,
    isLoading,
    themes,
    
    // Actions
    changeTheme,
    toggleTheme,
    
    // Utilities
    getCurrentTheme,
    isDarkTheme,
    getThemeList
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeContext;
