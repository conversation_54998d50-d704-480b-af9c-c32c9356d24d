/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global Styles */
:root {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: hsl(var(--b1));
  color: hsl(var(--bc));
  transition: background-color 0.3s ease, color 0.3s ease;
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--b2));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--b3));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--p));
}

/* Layout Utilities */
.sidebar-width {
  width: 280px;
}

.main-content-with-sidebar {
  margin-left: 0;
  transition: margin-left 0.3s ease;
}

@media (min-width: 1024px) {
  .main-content-with-sidebar.sidebar-open {
    margin-left: 280px;
  }
}

/* Custom Components */
@layer components {
  .btn-primary-custom {
    @apply bg-primary text-white hover:bg-primary-focus transition-all duration-200 ease-in-out transform hover:scale-105;
  }

  .card-custom {
    @apply bg-base-100 shadow-lg rounded-lg border border-base-300 hover:shadow-xl transition-all duration-300 ease-in-out p-6;
  }

  .input-custom {
    @apply input input-bordered w-full focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200;
  }

  .sidebar-item {
    @apply flex items-center px-4 py-3 text-base-content hover:bg-base-200 rounded-lg transition-all duration-200 cursor-pointer;
  }

  .sidebar-item.active {
    @apply bg-primary text-primary-content;
  }

  .navbar-custom {
    @apply bg-base-100 shadow-sm border-b border-base-300 backdrop-blur-sm;
  }

  .dashboard-card {
    @apply bg-base-100 rounded-xl shadow-md p-6 border border-base-300 hover:shadow-lg transition-all duration-300;
  }

  .stat-card {
    @apply bg-gradient-to-br from-primary to-primary-focus text-primary-content rounded-xl p-6 shadow-lg;
  }

  .page-container {
    @apply min-h-screen bg-base-200;
  }

  .content-wrapper {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6;
  }

  .form-container {
    @apply max-w-md mx-auto bg-base-100 rounded-xl shadow-lg p-8 border border-base-300;
  }

  .responsive-grid {
    @apply grid gap-6;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .stats-grid {
    @apply grid gap-4;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .dashboard-grid {
    @apply grid gap-6;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }

  /* Button enhancements */
  .btn {
    @apply transition-all duration-200 ease-in-out;
  }

  .btn:hover {
    @apply transform -translate-y-0.5;
  }

  /* Card enhancements */
  .card {
    @apply transition-all duration-300 ease-in-out;
  }

  /* Form enhancements */
  .form-control:focus-within {
    @apply ring-2 ring-primary ring-opacity-50;
  }

  /* Alert styles */
  .alert {
    @apply border rounded-lg p-4 mb-4;
  }

  .alert-success {
    @apply bg-success bg-opacity-10 border-success text-success;
  }

  .alert-error {
    @apply bg-error bg-opacity-10 border-error text-error;
  }

  .alert-warning {
    @apply bg-warning bg-opacity-10 border-warning text-warning;
  }

  .alert-info {
    @apply bg-info bg-opacity-10 border-info text-info;
  }

  /* Loading states */
  .skeleton {
    @apply animate-pulse bg-base-300 rounded;
  }

  /* Mobile optimizations */
  @media (max-width: 768px) {
    .content-wrapper {
      @apply px-4 py-4;
    }

    .form-container {
      @apply p-6 mx-4;
    }

    .card-custom {
      @apply p-4;
    }

    .dashboard-card {
      @apply p-4;
    }

    .stats-grid {
      grid-template-columns: 1fr;
    }

    .responsive-grid {
      grid-template-columns: 1fr;
    }

    .dashboard-grid {
      grid-template-columns: 1fr;
    }
  }

  /* Tablet optimizations */
  @media (min-width: 769px) and (max-width: 1024px) {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .responsive-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.bounce-subtle {
  animation: bounceSubtle 0.6s ease-in-out;
}

/* Loading Animation */
.loading-spinner {
  @apply inline-block w-8 h-8 border-4 border-current border-t-transparent rounded-full animate-spin;
}

/* Toast Notifications Custom Styles */
.Toastify__toast {
  @apply rounded-lg shadow-lg;
}

.Toastify__toast--success {
  @apply bg-success text-success-content;
}

.Toastify__toast--error {
  @apply bg-error text-error-content;
}

.Toastify__toast--warning {
  @apply bg-warning text-warning-content;
}

.Toastify__toast--info {
  @apply bg-info text-info-content;
}
