/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Import Bootstrap CSS */
@import 'bootstrap/dist/css/bootstrap.min.css';

/* Import Font Awesome */
@import '@fortawesome/fontawesome-free/css/all.min.css';

/* Import AOS */
@import 'aos/dist/aos.css';

/* Import Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global Styles */
:root {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: theme('colors.base-100');
  color: theme('colors.base-content');
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: theme('colors.base-200');
}

::-webkit-scrollbar-thumb {
  background: theme('colors.base-300');
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: theme('colors.primary');
}

/* Custom Components */
@layer components {
  .btn-primary-custom {
    @apply bg-primary text-white hover:bg-primary-focus transition-all duration-200 ease-in-out transform hover:scale-105;
  }

  .card-custom {
    @apply bg-base-100 shadow-lg rounded-lg border border-base-300 hover:shadow-xl transition-all duration-300 ease-in-out;
  }

  .input-custom {
    @apply input input-bordered w-full focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-200;
  }

  .sidebar-item {
    @apply flex items-center px-4 py-3 text-base-content hover:bg-base-200 rounded-lg transition-all duration-200 cursor-pointer;
  }

  .sidebar-item.active {
    @apply bg-primary text-primary-content;
  }

  .navbar-custom {
    @apply bg-base-100 shadow-sm border-b border-base-300;
  }

  .dashboard-card {
    @apply bg-base-100 rounded-xl shadow-md p-6 border border-base-300 hover:shadow-lg transition-all duration-300;
  }

  .stat-card {
    @apply bg-gradient-to-br from-primary to-primary-focus text-primary-content rounded-xl p-6 shadow-lg;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.bounce-subtle {
  animation: bounceSubtle 0.6s ease-in-out;
}

/* Loading Animation */
.loading-spinner {
  @apply inline-block w-8 h-8 border-4 border-current border-t-transparent rounded-full animate-spin;
}

/* Toast Notifications Custom Styles */
.Toastify__toast {
  @apply rounded-lg shadow-lg;
}

.Toastify__toast--success {
  @apply bg-success text-success-content;
}

.Toastify__toast--error {
  @apply bg-error text-error-content;
}

.Toastify__toast--warning {
  @apply bg-warning text-warning-content;
}

.Toastify__toast--info {
  @apply bg-info text-info-content;
}
