// Global SCSS Variables and Mixins
$primary-color: #3b82f6;
$secondary-color: #64748b;
$success-color: #10b981;
$warning-color: #f59e0b;
$error-color: #ef4444;
$info-color: #0ea5e9;

// Breakpoints
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

// Spacing
$spacing-xs: 0.25rem;
$spacing-sm: 0.5rem;
$spacing-md: 1rem;
$spacing-lg: 1.5rem;
$spacing-xl: 2rem;
$spacing-2xl: 3rem;

// Border radius
$border-radius-sm: 0.25rem;
$border-radius-md: 0.5rem;
$border-radius-lg: 0.75rem;
$border-radius-xl: 1rem;

// Shadows
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin transition-smooth {
  transition: all 0.3s ease-in-out;
}

@mixin hover-lift {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
}

@mixin button-base {
  padding: $spacing-sm $spacing-lg;
  border-radius: $border-radius-md;
  font-weight: 500;
  cursor: pointer;
  border: none;
  @include transition-smooth;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

@mixin card-base {
  background: white;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-md;
  padding: $spacing-lg;
  @include transition-smooth;
  
  &:hover {
    box-shadow: $shadow-lg;
  }
}

// Responsive mixins
@mixin mobile-only {
  @media (max-width: #{$breakpoint-md - 1px}) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: #{$breakpoint-md}) {
    @content;
  }
}

@mixin desktop-up {
  @media (min-width: #{$breakpoint-lg}) {
    @content;
  }
}

@mixin large-desktop-up {
  @media (min-width: #{$breakpoint-xl}) {
    @content;
  }
}

// Animation keyframes
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// Utility classes
.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.4s ease-out;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

// Text utilities
.text-gradient {
  background: linear-gradient(135deg, $primary-color, $info-color);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// Custom scrollbar for webkit browsers
.custom-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $primary-color;
    border-radius: 3px;
    
    &:hover {
      background: darken($primary-color, 10%);
    }
  }
}
