import React from 'react';
import { motion } from 'framer-motion';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

// Custom tooltip component
const CustomTooltip = ({ active, payload, label, type = 'default' }) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-base-100 border border-base-300 rounded-lg shadow-lg p-3">
        <p className="text-sm font-medium text-base-content mb-1">{label}</p>
        {payload.map((entry, index) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {`${entry.name}: ${entry.value}${type === 'percentage' ? '%' : ''}`}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// Line Chart Component
export const LineProgressChart = ({ 
  data = [], 
  dataKey = 'value', 
  xAxisKey = 'name',
  title = 'Progress Over Time',
  color = '#3b82f6',
  height = 300,
  showGrid = true,
  showTooltip = true,
  showLegend = false,
  className = ''
}) => {
  return (
    <motion.div
      className={`bg-base-100 rounded-lg p-4 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {title && (
        <h3 className="text-lg font-semibold text-base-content mb-4">{title}</h3>
      )}
      <ResponsiveContainer width="100%" height={height}>
        <LineChart data={data}>
          {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="currentColor" opacity={0.1} />}
          <XAxis 
            dataKey={xAxisKey} 
            stroke="currentColor" 
            fontSize={12}
            opacity={0.7}
          />
          <YAxis 
            stroke="currentColor" 
            fontSize={12}
            opacity={0.7}
          />
          {showTooltip && <Tooltip content={<CustomTooltip />} />}
          {showLegend && <Legend />}
          <Line
            type="monotone"
            dataKey={dataKey}
            stroke={color}
            strokeWidth={3}
            dot={{ fill: color, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: color, strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </motion.div>
  );
};

// Area Chart Component
export const AreaProgressChart = ({ 
  data = [], 
  dataKey = 'value', 
  xAxisKey = 'name',
  title = 'Progress Area',
  color = '#3b82f6',
  height = 300,
  showGrid = true,
  showTooltip = true,
  className = ''
}) => {
  return (
    <motion.div
      className={`bg-base-100 rounded-lg p-4 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {title && (
        <h3 className="text-lg font-semibold text-base-content mb-4">{title}</h3>
      )}
      <ResponsiveContainer width="100%" height={height}>
        <AreaChart data={data}>
          {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="currentColor" opacity={0.1} />}
          <XAxis 
            dataKey={xAxisKey} 
            stroke="currentColor" 
            fontSize={12}
            opacity={0.7}
          />
          <YAxis 
            stroke="currentColor" 
            fontSize={12}
            opacity={0.7}
          />
          {showTooltip && <Tooltip content={<CustomTooltip />} />}
          <Area
            type="monotone"
            dataKey={dataKey}
            stroke={color}
            fill={color}
            fillOpacity={0.3}
            strokeWidth={2}
          />
        </AreaChart>
      </ResponsiveContainer>
    </motion.div>
  );
};

// Bar Chart Component
export const BarProgressChart = ({ 
  data = [], 
  dataKey = 'value', 
  xAxisKey = 'name',
  title = 'Progress Bars',
  color = '#3b82f6',
  height = 300,
  showGrid = true,
  showTooltip = true,
  className = ''
}) => {
  return (
    <motion.div
      className={`bg-base-100 rounded-lg p-4 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {title && (
        <h3 className="text-lg font-semibold text-base-content mb-4">{title}</h3>
      )}
      <ResponsiveContainer width="100%" height={height}>
        <BarChart data={data}>
          {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="currentColor" opacity={0.1} />}
          <XAxis 
            dataKey={xAxisKey} 
            stroke="currentColor" 
            fontSize={12}
            opacity={0.7}
          />
          <YAxis 
            stroke="currentColor" 
            fontSize={12}
            opacity={0.7}
          />
          {showTooltip && <Tooltip content={<CustomTooltip />} />}
          <Bar dataKey={dataKey} fill={color} radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </motion.div>
  );
};

// Pie Chart Component
export const PieProgressChart = ({ 
  data = [], 
  dataKey = 'value', 
  nameKey = 'name',
  title = 'Progress Distribution',
  colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'],
  height = 300,
  showTooltip = true,
  showLegend = true,
  className = ''
}) => {
  return (
    <motion.div
      className={`bg-base-100 rounded-lg p-4 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {title && (
        <h3 className="text-lg font-semibold text-base-content mb-4">{title}</h3>
      )}
      <ResponsiveContainer width="100%" height={height}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            outerRadius={80}
            fill="#8884d8"
            dataKey={dataKey}
            nameKey={nameKey}
            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
            ))}
          </Pie>
          {showTooltip && <Tooltip content={<CustomTooltip type="percentage" />} />}
          {showLegend && <Legend />}
        </PieChart>
      </ResponsiveContainer>
    </motion.div>
  );
};

// Simple Progress Bar Component
export const SimpleProgressBar = ({ 
  value = 0, 
  max = 100, 
  label = '', 
  color = 'primary',
  size = 'md',
  showPercentage = true,
  animated = true,
  className = ''
}) => {
  const percentage = Math.min((value / max) * 100, 100);
  
  const sizeClasses = {
    sm: 'h-2',
    md: 'h-3',
    lg: 'h-4'
  };

  const colorClasses = {
    primary: 'bg-primary',
    success: 'bg-success',
    warning: 'bg-warning',
    error: 'bg-error',
    info: 'bg-info'
  };

  return (
    <motion.div
      className={`w-full ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {(label || showPercentage) && (
        <div className="flex justify-between items-center mb-2">
          {label && <span className="text-sm font-medium text-base-content">{label}</span>}
          {showPercentage && (
            <span className="text-sm text-base-content/60">{percentage.toFixed(0)}%</span>
          )}
        </div>
      )}
      <div className={`w-full bg-base-300 rounded-full ${sizeClasses[size]}`}>
        <motion.div
          className={`${sizeClasses[size]} rounded-full ${colorClasses[color]} ${animated ? 'transition-all duration-500 ease-out' : ''}`}
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={{ duration: 1, ease: "easeOut" }}
        />
      </div>
    </motion.div>
  );
};

// Circular Progress Component
export const CircularProgress = ({ 
  value = 0, 
  max = 100, 
  size = 120,
  strokeWidth = 8,
  color = '#3b82f6',
  backgroundColor = '#e5e7eb',
  showPercentage = true,
  label = '',
  className = ''
}) => {
  const percentage = Math.min((value / max) * 100, 100);
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = `${circumference} ${circumference}`;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <motion.div
      className={`flex flex-col items-center ${className}`}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="relative">
        <svg
          width={size}
          height={size}
          className="transform -rotate-90"
        >
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={backgroundColor}
            strokeWidth={strokeWidth}
            fill="transparent"
          />
          {/* Progress circle */}
          <motion.circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={color}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeLinecap="round"
            initial={{ strokeDashoffset: circumference }}
            animate={{ strokeDashoffset }}
            transition={{ duration: 1, ease: "easeOut" }}
          />
        </svg>
        {showPercentage && (
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-lg font-semibold text-base-content">
              {percentage.toFixed(0)}%
            </span>
          </div>
        )}
      </div>
      {label && (
        <span className="mt-2 text-sm font-medium text-base-content text-center">
          {label}
        </span>
      )}
    </motion.div>
  );
};

// Main ProgressChart component that can render different chart types
const ProgressChart = ({ type = 'line', ...props }) => {
  switch (type) {
    case 'line':
      return <LineProgressChart {...props} />;
    case 'area':
      return <AreaProgressChart {...props} />;
    case 'bar':
      return <BarProgressChart {...props} />;
    case 'pie':
      return <PieProgressChart {...props} />;
    case 'progress':
      return <SimpleProgressBar {...props} />;
    case 'circular':
      return <CircularProgress {...props} />;
    default:
      return <LineProgressChart {...props} />;
  }
};

export default ProgressChart;
